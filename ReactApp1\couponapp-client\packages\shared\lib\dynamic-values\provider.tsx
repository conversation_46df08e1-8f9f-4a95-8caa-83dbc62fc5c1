import React, { createContext, useContext, useMemo, useCallback, useRef, useEffect } from 'react'
import { DynamicValuesContextValue, ValueSourceCatalog, ValueSubscription } from './types'
import { scanCampaignForValueSources } from './scanner'
import { useCampaignData } from '../hooks/useCampaignStore'

const DynamicValuesContext = createContext<DynamicValuesContextValue | null>(null)

interface DynamicValuesProviderProps {
  children: React.ReactNode
}

export function DynamicValuesProvider({  children }: DynamicValuesProviderProps) {

  const campaignData = useCampaignData()

  const catalog = useMemo(() => {
    if (!campaignData) return {}
    return scanCampaignForValueSources(campaignData)
  }, [campaignData])

  // Storage for current values (in-memory for now)
  const valuesRef = useRef<Map<string, any>>(new Map())
  
  // Storage for subscriptions
  const subscriptionsRef = useRef<Map<string, Set<(value: any) => void>>>(new Map())

  // Get current value for a path
  const getValue = useCallback((path: string): any => {
    return valuesRef.current.get(path)
  }, [])

  // Set value and notify subscribers
  const setValue = useCallback((path: string, value: any): void => {
    const previousValue = valuesRef.current.get(path)
    
    // Only update if value actually changed
    if (previousValue !== value) {
      valuesRef.current.set(path, value)
      
      // Notify all subscribers for this path
      const subscribers = subscriptionsRef.current.get(path)
      if (subscribers) {
        subscribers.forEach(callback => {
          try {
            callback(value)
          } catch (error) {
            console.error(`Error in dynamic value subscription callback for path '${path}':`, error)
          }
        })
      }
    }
  }, [])

  // Subscribe to value changes
  const subscribe = useCallback((path: string, callback: (value: any) => void): (() => void) => {
    // Initialize subscribers set for this path if not exists
    if (!subscriptionsRef.current.has(path)) {
      subscriptionsRef.current.set(path, new Set())
    }
    
    const subscribers = subscriptionsRef.current.get(path)!
    subscribers.add(callback)
    
    // Return unsubscribe function
    return () => {
      subscribers.delete(callback)
      
      // Clean up empty subscriber sets
      if (subscribers.size === 0) {
        subscriptionsRef.current.delete(path)
      }
    }
  }, [])

  // Check if the system is ready (has campaign data)
  const isReady = useMemo(() => {
    return !!campaignData && Object.keys(catalog).length >= 0
  }, [campaignData, catalog])

  // Context value
  const contextValue: DynamicValuesContextValue = useMemo(() => ({
    catalog,
    getValue,
    setValue,
    subscribe,
    isReady
  }), [catalog, getValue, setValue, subscribe, isReady])

  return (
    <DynamicValuesContext.Provider value={contextValue}>
      {children}
    </DynamicValuesContext.Provider>
  )
}

/**
 * Hook to access the dynamic values context
 * @returns The dynamic values context value
 * @throws Error if used outside of DynamicValuesProvider
 */
export function useDynamicValuesContext(): DynamicValuesContextValue {
  const context = useContext(DynamicValuesContext)
  
  if (!context) {
    throw new Error('useDynamicValuesContext must be used within a DynamicValuesProvider')
  }
  
  return context
}
