import React, { useState, useMemo } from 'react'
import { AlertCircle, Eye, EyeOff, Plus } from 'lucide-react'
import { Label } from '@repo/shared/components/ui/label'
import { Textarea } from '@repo/shared/components/ui/textarea'
import { Button } from '@repo/shared/components/ui/button'
import { Alert, AlertDescription } from '@repo/shared/components/ui/alert'
import { Badge } from '@repo/shared/components/ui/badge'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@repo/shared/components/ui/collapsible'
import { ValueSourcePicker } from './ValueSourcePicker'
import { useDynamicValues, getAllAvailablePaths } from '../hooks/useDynamicValues'
import { 
  parseExpression, 
  renderExpression, 
  validateExpression, 
  hasDynamicContent 
} from '../expression-parser'
import { cn } from '@repo/shared/lib/utils'

interface DynamicTextEditorProps {
  value: string
  onChange: (value: string) => void
  label?: string
  placeholder?: string
  className?: string
  showPreview?: boolean
}

export function DynamicTextEditor({
  value,
  onChange,
  label = "Text Content",
  placeholder = "Enter text with dynamic values like: Score: {game/123456/score}",
  className,
  showPreview = true
}: DynamicTextEditorProps) {
  const { availableSources, getValue } = useDynamicValues()
  const [showPicker, setShowPicker] = useState(false)
  const [showPreviewPanel, setShowPreviewPanel] = useState(showPreview)
  
  // Get all available paths for validation
  const availablePaths = useMemo(() => 
    getAllAvailablePaths(availableSources), 
    [availableSources]
  )
  
  // Parse and validate the current expression
  const validation = useMemo(() => 
    validateExpression(value, availablePaths), 
    [value, availablePaths]
  )
  
  // Render preview
  const previewText = useMemo(() => 
    renderExpression(value, getValue, '—'), 
    [value, getValue]
  )
  
  // Check if content is dynamic
  const isDynamic = hasDynamicContent(value)
  
  // Handle inserting a variable at cursor position
  const insertVariable = (path: string) => {
    const textarea = document.querySelector('textarea[data-dynamic-text-editor]') as HTMLTextAreaElement
    if (!textarea) {
      // Fallback: append to end
      onChange(value + `{${path}}`)
      return
    }
    
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const variable = `{${path}}`
    
    const newValue = value.slice(0, start) + variable + value.slice(end)
    onChange(newValue)
    
    // Restore cursor position after the inserted variable
    setTimeout(() => {
      textarea.focus()
      textarea.setSelectionRange(start + variable.length, start + variable.length)
    }, 0)
    
    setShowPicker(false)
  }
  
  return (
    <div className={cn("space-y-3", className)}>
      {/* Label and status */}
      <div className="flex items-center justify-between">
        <Label className="text-sm font-medium">{label}</Label>
        <div className="flex items-center gap-2">
          {isDynamic && (
            <Badge variant="secondary" className="text-xs">
              Dynamic
            </Badge>
          )}
          {showPreview && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPreviewPanel(!showPreviewPanel)}
              className="h-6 px-2"
            >
              {showPreviewPanel ? (
                <EyeOff className="h-3 w-3" />
              ) : (
                <Eye className="h-3 w-3" />
              )}
            </Button>
          )}
        </div>
      </div>
      
      {/* Text editor */}
      <div className="space-y-2">
        <Textarea
          data-dynamic-text-editor
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder={placeholder}
          className={cn(
            "min-h-[100px] font-mono text-sm",
            !validation.isValid && "border-destructive"
          )}
        />
        
        {/* Insert variable button */}
        <div className="flex justify-between items-center">
          <Collapsible open={showPicker} onOpenChange={setShowPicker}>
            <CollapsibleTrigger asChild>
              <Button variant="outline" size="sm" className="text-xs">
                <Plus className="h-3 w-3 mr-1" />
                Insert Variable
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="mt-2">
              <div className="border rounded-md p-3 bg-background">
                <ValueSourcePicker
                  onSelect={insertVariable}
                  placeholder="Search for a value to insert..."
                />
              </div>
            </CollapsibleContent>
          </Collapsible>
          
          {/* Variable count */}
          {isDynamic && (
            <div className="text-xs text-muted-foreground">
              {validation.unknownPaths.length > 0 
                ? `${validation.unknownPaths.length} unknown variables`
                : `${parseExpression(value).variables.length} variables`
              }
            </div>
          )}
        </div>
      </div>
      
      {/* Validation errors */}
      {!validation.isValid && validation.errors.length > 0 && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {validation.errors.map((error, index) => (
                <div key={index} className="text-sm">{error}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      
      {/* Validation warnings */}
      {validation.warnings.length > 0 && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {validation.warnings.map((warning, index) => (
                <div key={index} className="text-sm">{warning}</div>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}
      
      {/* Preview */}
      {showPreview && showPreviewPanel && (
        <div className="space-y-2">
          <Label className="text-sm font-medium">Preview</Label>
          <div className="p-3 border rounded-md bg-muted/50 min-h-[60px] text-sm">
            {previewText || (
              <span className="text-muted-foreground italic">
                Preview will appear here...
              </span>
            )}
          </div>
        </div>
      )}
    </div>
  )
}
