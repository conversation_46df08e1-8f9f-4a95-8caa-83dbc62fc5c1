import { Widget } from '../types/editor'
import { getRegisteredSources } from './registry'
import { DynamicValueSource, ResolvedValueSource, ValueSourceCatalog } from './types'

/**
 * Extract category from a path (first segment before /)
 * @param path - The value source path
 * @returns The category (e.g., "game", "timer", "form")
 */
function extractCategory(path: string): string {
  return path.split('/')[0]
}

/**
 * Resolve template variables in a path
 * @param path - Path with template variables like "game/{widgetId}/score"
 * @param widgetId - The actual widget ID to substitute
 * @returns Resolved path like "game/123456/score"
 */
function resolvePath(path: string, widgetId: string): string {
  return path.replace('{widgetId}', widgetId)
}

/**
 * Convert a DynamicValueSource to ResolvedValueSource
 * @param source - The source definition with template variables
 * @param widgetId - The widget ID
 * @param widgetName - Display name for the widget
 * @param componentType - The component type
 * @returns Resolved value source
 */
function resolveValueSource(
  source: DynamicValueSource,
  widgetId: string,
  widgetName: string,
  componentType: string
): ResolvedValueSource {
  return {
    path: resolvePath(source.path, widgetId),
    type: source.type,
    label: source.label,
    description: source.description,
    widgetId,
    widgetName,
    componentType
  }
}

/**
 * Get widget display name for UI
 * @param widget - The widget object
 * @returns A human-readable name for the widget
 */
function getWidgetDisplayName(widget: Widget): string {
  // Try to get a meaningful name from widget settings or use a default
  if (widget.settings?.name) {
    return widget.settings.name
  }
  
  // For game widgets, try to get game name
  if (widget.componentName === 'game' && widget.settings?.gameId) {
    const gameId = widget.settings.gameId
    const gameName = gameId.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    return `${gameName} (${widget.id.slice(0, 6)})`
  }
  
  // Default fallback
  const componentName = widget.componentName.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  return `${componentName} (${widget.id.slice(0, 6)})`
}

/**
 * Recursively scan widgets in a tree structure
 * @param widgets - Array of widgets to scan
 * @returns Array of resolved value sources
 */
function scanWidgets(widgets: Widget[]): ResolvedValueSource[] {
  const resolvedSources: ResolvedValueSource[] = []
  
  for (const widget of widgets) {
    // Determine component type for value source lookup
    let componentType = widget.componentName

    // For game widgets, use the specific game ID
    if (widget.componentName === 'GameWidget' && widget.settings?.gameId) {
      componentType = widget.settings.gameId
      console.log(`Found GameWidget with gameId: ${componentType}, widgetId: ${widget.id}`)
    }

    // Get registered sources for this component type
    const registeredSources = getRegisteredSources(componentType)

    if (registeredSources.length > 0) {
      console.log(`Found ${registeredSources.length} registered sources for component type: ${componentType}`)
      const widgetName = getWidgetDisplayName(widget)

      // Resolve all sources for this widget
      for (const source of registeredSources) {
        const resolved = resolveValueSource(source, widget.id, widgetName, componentType)
        resolvedSources.push(resolved)
        console.log(`Resolved value source: ${resolved.path}`)
      }
    }
    
    // Recursively scan children
    if (widget.children && widget.children.length > 0) {
      resolvedSources.push(...scanWidgets(widget.children))
    }
  }
  
  return resolvedSources
}

/**
 * Build hierarchical catalog from resolved sources
 * @param resolvedSources - Array of resolved value sources
 * @returns Hierarchical catalog organized by category and widget
 */
function buildCatalog(resolvedSources: ResolvedValueSource[]): ValueSourceCatalog {
  const catalog: ValueSourceCatalog = {}
  
  for (const source of resolvedSources) {
    const category = extractCategory(source.path)
    
    // Initialize category if not exists
    if (!catalog[category]) {
      catalog[category] = {}
    }
    
    // Initialize widget entry if not exists
    if (!catalog[category][source.widgetId]) {
      catalog[category][source.widgetId] = {
        widgetName: source.widgetName || source.widgetId,
        componentType: source.componentType,
        sources: []
      }
    }
    
    // Add source to widget
    catalog[category][source.widgetId].sources.push(source)
  }
  
  return catalog
}

/**
 * Scan campaign structure and build value source catalog
 * @param campaignData - The campaign data containing all widgets
 * @returns Hierarchical catalog of available value sources
 */
export function scanCampaignForValueSources(campaignData: any): ValueSourceCatalog {
  if (!campaignData?.campaign?.scenes) {
    return {}
  }
  
  const allWidgets: Widget[] = []
  
  // Extract all widgets from all scenes
  for (const scene of campaignData.campaign.scenes) {
    if (scene.widgets && Array.isArray(scene.widgets)) {
      allWidgets.push(...scene.widgets)
    }
  }
  
  // Scan widgets and resolve sources
  const resolvedSources = scanWidgets(allWidgets)
  
  // Build hierarchical catalog
  return buildCatalog(resolvedSources)
}
