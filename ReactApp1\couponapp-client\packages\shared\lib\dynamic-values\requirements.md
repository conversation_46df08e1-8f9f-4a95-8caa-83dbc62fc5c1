# Dynamic Values System - Requirements & Architecture

## Overview
A decoupled system for cross-component data communication that allows any component to expose live data and any other component to consume it. Think of it as a universal data sharing bus with a folder-like structure.

## Core Concept
Components publish live values to **hierarchical paths** (like a file system), and other components can subscribe to these values. The system is completely agnostic to what's publishing or consuming the data.

## Path Structure
Values are organized in a hierarchical folder-like structure:

```
game/
├── {widgetId}/
│   ├── score
│   ├── lives
│   ├── progress/
│   │   ├── level
│   │   └── experience
│   └── stats/
│       ├── correctAnswers
│       └── wrongAnswers
timer/
├── {widgetId}/
│   ├── timeLeft
│   ├── isFinished
│   └── isPaused
form/
├── {widgetId}/
│   ├── submissionCount
│   ├── errors/
│   │   ├── count
│   │   └── messages
│   └── fields/
│       ├── email/isValid
│       └── name/value
custom/
├── analytics/
│   ├── pageViews
│   └── sessionDuration
└── user/
    ├── preferences/theme
    └── activity/lastAction
```

## Key Features

### 1. Hierarchical Organization
- Paths work like folders: `game/{widgetId}/stats/correctAnswers`
- Natural grouping: all game data under `game/`, all timer data under `timer/`
- Nested structures: `form/{widgetId}/errors/count`

### 2. Widget ID Templating
- Use `{widgetId}` in registration: `game/{widgetId}/score`
- System resolves to actual IDs: `game/123456/score`
- Allows multiple instances of same component type

### 3. Static Campaign Analysis
- No dependency on mounted components
- Scans entire campaign structure (all pages, scenes, widgets)
- Works across editor tabs and pages
- Resolves game types from widget settings (`widget.settings.gameId`)
- Maps game types to their registered value sources

### 4. Type Safety
- Each value source has defined type (number, string, boolean, object)
- Runtime type checking and validation
- Editor autocomplete and validation

## File Structure

```
packages/shared/lib/dynamic-values/
├── index.ts                          // Main exports
├── requirements.md                   // This file
├── types.ts                          // TypeScript definitions
├── registry.ts                       // Registration functions
├── scanner.ts                        // Campaign structure analysis
├── provider.tsx                      // React provider component
├── hooks/
│   ├── useDynamicValues.ts          // Access available sources
│   └── useLiveValue.ts              // Subscribe to specific value
└── components/
    └── ValueSourcePicker.tsx         // Hierarchical UI picker
```

## Core Types

```typescript
interface DynamicValueSource {
  path: string                        // "game/{widgetId}/stats/score"
  type: 'number' | 'string' | 'boolean' | 'object'
  label: string                       // "Player Score"
  description?: string                // "Current player's game score"
}

interface ValueSourceCatalog {
  // Hierarchical structure built from paths
  // game/123456/score -> { game: { "123456": { sources: [...] } } }
}
```

## Campaign Analysis Process

```typescript
// How the scanner resolves game-specific value sources
function scanGameWidget(widget: Widget) {
  const gameId = widget.settings.gameId     // "quiz-game", "memory-game", etc.
  const valueSources = getRegisteredSources(gameId)

  return valueSources.map(source => ({
    ...source,
    path: source.path.replace('{widgetId}', widget.id)
  }))
}

// Example resolution:
// Widget: { id: "123456", settings: { gameId: "quiz-game" } }
// Registered: "game/{widgetId}/score"
// Resolved: "game/123456/score"
```

## Registration API

```typescript
// Game-specific registration (gameId matches the game type)
registerValueSources('quiz-game', [
  {
    path: "game/{widgetId}/score",
    type: "number",
    label: "Player Score"
  },
  {
    path: "game/{widgetId}/stats/correctAnswers",
    type: "number",
    label: "Correct Answers"
  }
])

registerValueSources('memory-game', [
  {
    path: "game/{widgetId}/score",
    type: "number",
    label: "Current Score"
  },
  {
    path: "game/{widgetId}/matches",
    type: "number",
    label: "Successful Matches"
  }
])

// Widget-specific registration (for non-game widgets)
registerValueSources('timer-widget', [
  {
    path: "timer/{widgetId}/timeLeft",
    type: "number",
    label: "Time Remaining"
  }
])
```

## Consumer API

```typescript
// Provider (one-liner at campaign root)
<DynamicValuesProvider campaignData={campaignData}>
  <CampaignEditor />
</DynamicValuesProvider>

// Hook for available sources
const { availableSources, getValue } = useDynamicValues()

// Hook for live value subscription
const playerScore = useLiveValue("game/123456/score", 0)

// Picker component
<ValueSourcePicker 
  onSelect={(path) => setDataSource(path)}
  selectedPath={currentDataSource}
/>
```

## UI Requirements

### Value Source Picker
- Hierarchical tree view matching path structure
- Expandable folders (game/, timer/, form/)
- Widget instances grouped under type
- Individual values as selectable items
- Search/filter functionality
- Clear visual hierarchy

### Text Widget Integration
- "Dynamic Content" toggle in settings
- Value source picker when enabled
- Expression syntax: `"Score: {game/123456/score}"`
- Multiple values in one text: `"Score: {game/123456/score} | Lives: {game/123456/lives}"`
- Real-time preview in editor

## Implementation Phases

### Phase 1: Core System ✅
- [x] Type definitions
- [x] Registration system
- [x] Campaign scanner
- [x] Provider component
- [x] Basic hooks

### Phase 2: UI Components ✅
- [x] Value source picker
- [x] Hierarchical tree view
- [x] Search functionality
- [x] Expression parser for text widgets
- [x] Dynamic text editor component
- [x] Runtime dynamic text components

## Implementation Complete! 🎉

The Dynamic Values system is now fully implemented and ready to use. Here's what we built:

### Core System
- **Type-safe registration**: `registerValueSources('quiz-game', [...])`
- **Campaign scanning**: Automatically discovers all value sources from campaign structure
- **Provider setup**: One-liner `<DynamicValuesProvider campaignData={campaignData}>`
- **Live subscriptions**: Real-time updates with `useLiveValue()` and `useLiveValues()`
- **Publishing**: Components can publish values with `usePublishValue()`

### UI Components
- **ValueSourcePicker**: Hierarchical tree view with search and filtering
- **DynamicTextEditor**: Rich editor for creating expressions with variable insertion
- **DynamicText**: Runtime components for displaying live dynamic content
- **Expression Parser**: Full support for `{path/to/value}` syntax with validation

### Key Features
- **Hierarchical paths**: `game/{widgetId}/stats/score` folder-like structure
- **Game-specific sources**: Different games expose different value types
- **Real-time updates**: All consumers update automatically when values change
- **Type safety**: Full TypeScript support with validation
- **Search & discovery**: Easy to find and select available value sources
- **Expression validation**: Helpful error messages and warnings

## Success Criteria ✅
1. ✅ Text widget can display live data from any component
2. ✅ Multiple components can expose same value types independently
3. ✅ System works across all editor pages/tabs
4. ✅ No coupling between data producers and consumers
5. ✅ Intuitive hierarchical UI for value selection
6. ✅ Type-safe value access and validation

## Ready for Integration
The system is now ready to be integrated into existing games and widgets. See `examples/ExampleUsage.tsx` for complete usage examples.
