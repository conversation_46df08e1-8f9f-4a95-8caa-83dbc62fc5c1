/**
 * Example usage of the Dynamic Values system
 * This file demonstrates how to integrate the system into your app
 */

import React, { useState } from 'react'
import { DynamicText, DynamicValue } from '../components/DynamicText'
import { DynamicTextEditor } from '../components/DynamicTextEditor'
import { ValueSourcePicker } from '../components/ValueSourcePicker'
import { useDynamicValues } from '../hooks/useDynamicValues'
import { usePublishValue } from '../hooks/useLiveValue'
import { DynamicValuesProvider } from '../provider'
import { registerValueSources } from '../registry'




// Example: Register value sources for a timer widget
registerValueSources('timer-widget', [
  {
    path: "timer/{widgetId}/timeLeft",
    type: "number",
    label: "Time Remaining",
    description: "Seconds remaining on timer"
  },
  {
    path: "timer/{widgetId}/isFinished",
    type: "boolean",
    label: "Timer Finished",
    description: "Whether the timer has finished"
  }
])

// Example campaign data structure
const exampleCampaignData = {
  campaign: {
    scenes: [
      {
        widgets: [
          {
            id: "game-widget-123",
            componentName: "GameWidget",
            settings: {
              gameId: "quiz-game",
              name: "Math Quiz"
            },
            children: []
          },
          {
            id: "timer-widget-456",
            componentName: "timer-widget",
            settings: {
              name: "Countdown Timer"
            },
            children: []
          }
        ]
      }
    ]
  }
}

// Example: Game component that publishes values
function ExampleGameComponent() {
  const publishScore = usePublishValue("game/game-widget-123/score")
  const publishLives = usePublishValue("game/game-widget-123/lives")
  const publishLevel = usePublishValue("game/game-widget-123/level")
  
  const [score, setScore] = useState(0)
  const [lives, setLives] = useState(3)
  const [level, setLevel] = useState("Easy")
  
  // Publish values when they change
  React.useEffect(() => {
    publishScore(score)
  }, [score, publishScore])
  
  React.useEffect(() => {
    publishLives(lives)
  }, [lives, publishLives])
  
  React.useEffect(() => {
    publishLevel(level)
  }, [level, publishLevel])
  
  return (
    <div className="p-4 border rounded-md space-y-4">
      <h3 className="font-bold">Quiz Game Controls</h3>
      
      <div className="space-y-2">
        <button 
          onClick={() => setScore(s => s + 10)}
          className="px-3 py-1 bg-blue-500 text-white rounded mr-2"
        >
          Add 10 Points
        </button>
        
        <button 
          onClick={() => setLives(l => Math.max(0, l - 1))}
          className="px-3 py-1 bg-red-500 text-white rounded mr-2"
        >
          Lose Life
        </button>
        
        <button 
          onClick={() => setLevel(level === "Easy" ? "Hard" : "Easy")}
          className="px-3 py-1 bg-green-500 text-white rounded"
        >
          Toggle Level
        </button>
      </div>
      
      <div className="text-sm text-gray-600">
        Current: Score {score}, Lives {lives}, Level {level}
      </div>
    </div>
  )
}

// Example: Text widget that consumes dynamic values
function ExampleTextWidget() {
  const [expression, setExpression] = useState("Score: {game/game-widget-123/score} | Lives: {game/game-widget-123/lives}")
  
  return (
    <div className="p-4 border rounded-md space-y-4">
      <h3 className="font-bold">Dynamic Text Widget</h3>
      
      <DynamicTextEditor
        value={expression}
        onChange={setExpression}
        label="Text Content"
        placeholder="Enter text with variables like {game/game-widget-123/score}"
      />
      
      <div className="p-3 bg-gray-50 rounded">
        <h4 className="font-medium mb-2">Live Output:</h4>
        <DynamicText expression={expression} className="text-lg" />
      </div>
    </div>
  )
}

// Example: Value source picker demo
function ExampleValueSourcePicker() {
  const [selectedPath, setSelectedPath] = useState<string>()
  const { availableSources } = useDynamicValues()
  
  return (
    <div className="p-4 border rounded-md space-y-4">
      <h3 className="font-bold">Value Source Picker</h3>
      
      <ValueSourcePicker
        onSelect={setSelectedPath}
        selectedPath={selectedPath}
      />
      
      {selectedPath && (
        <div className="p-3 bg-blue-50 rounded">
          <h4 className="font-medium">Selected Value:</h4>
          <div className="font-mono text-sm">{selectedPath}</div>
          <div className="mt-2">
            Current Value: <DynamicValue path={selectedPath} />
          </div>
        </div>
      )}
    </div>
  )
}

// Main example app
export function ExampleApp() {
  return (
    <DynamicValuesProvider campaignData={exampleCampaignData}>
      <div className="max-w-4xl mx-auto p-6 space-y-6">
        <h1 className="text-2xl font-bold">Dynamic Values System Example</h1>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ExampleGameComponent />
          <ExampleTextWidget />
        </div>
        
        <ExampleValueSourcePicker />
        
        <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
          <h3 className="font-bold text-yellow-800">How it works:</h3>
          <ol className="list-decimal list-inside text-sm text-yellow-700 mt-2 space-y-1">
            <li>Games register their value sources using <code>registerValueSources()</code></li>
            <li>The provider scans the campaign and builds a catalog of available sources</li>
            <li>Game components publish live values using <code>usePublishValue()</code></li>
            <li>Text widgets consume values using <code>DynamicText</code> or <code>useLiveValue()</code></li>
            <li>The picker helps users select available value sources</li>
            <li>Everything updates in real-time automatically!</li>
          </ol>
        </div>
      </div>
    </DynamicValuesProvider>
  )
}
