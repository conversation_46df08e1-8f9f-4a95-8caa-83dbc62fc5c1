import React, { useMemo } from 'react'
import { useLiveValues } from '../hooks/useLiveValue'
import { extractVariablePaths, renderExpression, hasDynamicContent } from '../expression-parser'

interface DynamicTextProps {
  expression: string
  fallbackValue?: string
  className?: string
  style?: React.CSSProperties
  children?: React.ReactNode
}

/**
 * Component that renders text with dynamic value substitution
 * Automatically subscribes to all variables in the expression and updates in real-time
 */
export function DynamicText({ 
  expression, 
  fallbackValue = '—', 
  className, 
  style,
  children 
}: DynamicTextProps) {
  // Extract all variable paths from the expression
  const variablePaths = useMemo(() => 
    extractVariablePaths(expression), 
    [expression]
  )
  
  // Subscribe to all variables for live updates
  const variableValues = useLiveValues(variablePaths)
  
  // Render the expression with current values
  const renderedText = useMemo(() => {
    if (!hasDynamicContent(expression)) {
      return expression
    }
    
    return renderExpression(
      expression,
      (path: string) => variableValues[path],
      fallbackValue
    )
  }, [expression, variableValues, fallbackValue])
  
  return (
    <span className={className} style={style}>
      {renderedText}
      {children}
    </span>
  )
}

interface DynamicTextBlockProps {
  expression: string
  fallbackValue?: string
  className?: string
  style?: React.CSSProperties
  as?: keyof JSX.IntrinsicElements
}

/**
 * Block-level component for dynamic text (div, p, h1, etc.)
 */
export function DynamicTextBlock({ 
  expression, 
  fallbackValue = '—', 
  className, 
  style,
  as: Component = 'div'
}: DynamicTextBlockProps) {
  // Extract all variable paths from the expression
  const variablePaths = useMemo(() => 
    extractVariablePaths(expression), 
    [expression]
  )
  
  // Subscribe to all variables for live updates
  const variableValues = useLiveValues(variablePaths)
  
  // Render the expression with current values
  const renderedText = useMemo(() => {
    if (!hasDynamicContent(expression)) {
      return expression
    }
    
    return renderExpression(
      expression,
      (path: string) => variableValues[path],
      fallbackValue
    )
  }, [expression, variableValues, fallbackValue])
  
  return (
    <Component className={className} style={style}>
      {renderedText}
    </Component>
  )
}

interface DynamicValueProps {
  path: string
  fallbackValue?: any
  formatter?: (value: any) => string
  className?: string
  style?: React.CSSProperties
}

/**
 * Component that displays a single dynamic value
 */
export function DynamicValue({ 
  path, 
  fallbackValue = '—', 
  formatter,
  className, 
  style 
}: DynamicValueProps) {
  // Subscribe to the specific path
  const value = useLiveValues([path])[path]
  
  // Format the value
  const displayValue = useMemo(() => {
    const currentValue = value !== undefined ? value : fallbackValue
    
    if (formatter) {
      return formatter(currentValue)
    }
    
    // Default formatting
    if (currentValue === null || currentValue === undefined) {
      return fallbackValue
    }
    
    if (typeof currentValue === 'boolean') {
      return currentValue ? 'Yes' : 'No'
    }
    
    if (typeof currentValue === 'number') {
      return currentValue.toString()
    }
    
    if (typeof currentValue === 'object') {
      return JSON.stringify(currentValue)
    }
    
    return String(currentValue)
  }, [value, fallbackValue, formatter])
  
  return (
    <span className={className} style={style}>
      {displayValue}
    </span>
  )
}

// Convenience components for common use cases
export const DynamicScore = ({ path, ...props }: Omit<DynamicValueProps, 'formatter'> & { path: string }) => (
  <DynamicValue 
    {...props} 
    path={path} 
    formatter={(value) => typeof value === 'number' ? value.toLocaleString() : String(value)}
  />
)

export const DynamicTimer = ({ path, ...props }: Omit<DynamicValueProps, 'formatter'> & { path: string }) => (
  <DynamicValue 
    {...props} 
    path={path} 
    formatter={(value) => {
      if (typeof value === 'number') {
        const minutes = Math.floor(value / 60)
        const seconds = value % 60
        return `${minutes}:${seconds.toString().padStart(2, '0')}`
      }
      return String(value)
    }}
  />
)

export const DynamicPercentage = ({ path, ...props }: Omit<DynamicValueProps, 'formatter'> & { path: string }) => (
  <DynamicValue 
    {...props} 
    path={path} 
    formatter={(value) => typeof value === 'number' ? `${Math.round(value * 100)}%` : String(value)}
  />
)
